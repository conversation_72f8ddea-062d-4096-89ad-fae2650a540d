import configs from '../configs';
import * as moment from 'moment';
import * as fs from 'fs';
import * as mkdirp from 'mkdirp';
import * as path from 'path';
import logger from './logger.service';
import * as readline from 'readline';
import { OrderService } from './order.service';
import { Order, ShipperType } from './interfaces/order.interface';
import { PaymentParmas } from './interfaces/payment.interface';
import {
    EsunAuthorizeRespondData,
    eSunSubscribeDetailData,
    IGenerateAuthorizeResponseResult,
} from './interfaces/esun.interface';
import {
    EsunAuthorizeDataRecord,
    EsunAuthorizeRespondDataRecord,
} from '../models/esun.model';
import * as shelljs from 'shelljs';
import * as iconv from 'iconv-lite';
import { CLINICO, SKD, IB } from '../const';
import { EventEmitter } from 'events';
import stringUtil from '../utils/string';
import fileUtil from '../utils/file';
import { Helpers } from '@clinico/clinico-node-framework';
import { AskingService } from './asking.service';
import { RepayService } from './repay.service';
import { ISubscribeDetail } from './interfaces/subscribe.interface';
import { FormService } from './form.service';

export class EsunService {
    public emitter: any;
    private company: string;
    private orderService: OrderService;
    private askingService: AskingService;
    private repayService: RepayService;
    private formService: FormService;

    constructor(company: string) {
        this.company = company;
        this.orderService = new OrderService(this.company);
        this.askingService = new AskingService(this.company);
        this.repayService = new RepayService(this.company);
        this.formService = new FormService(this.company);
        this.emitter = new EventEmitter();
    }

    async generateRequest(params: {
        orders: Order[];
        isRepay: boolean;
    }): Promise<EsunAuthorizeDataRecord[]> {
        try {
            let machineId = '';
            let shopId = '';
            let SN = '';
            switch (this.company) {
                case CLINICO:
                    machineId = configs.esun.clinico.machineId;
                    shopId = configs.esun.clinico.shopId;
                    SN = params.isRepay ? '04' : '01';
                    break;
                case SKD:
                    machineId = configs.esun.skd.machineId;
                    shopId = configs.esun.skd.shopId;
                    SN = params.isRepay ? '05' : '02';
                    break;
                case IB:
                    machineId = configs.esun.ib.machineId;
                    shopId = configs.esun.ib.shopId;
                    SN = params.isRepay ? '06' : '03';
                    break;
            }

            //授權
            const authorizeContext = await this.generateAuthorizeRequestRecord(
                {
                    machineId,
                    shopId,
                    isRepay: params.isRepay,
                },
                params.orders,
            );
            await this.generateRequestFile(
                SN,
                shopId,
                authorizeContext.request,
            );
            return authorizeContext.authorizeData;
        } catch (err) {
            console.error(err);
            throw err;
        }
    }

    private async generateAuthorizeRequestRecord(
        params: {
            machineId: string;
            shopId: string;
            isRepay: boolean;
        },
        orders: PaymentParmas[],
    ): Promise<{ request: string; authorizeData: EsunAuthorizeDataRecord[] }> {
        const authorizeData: EsunAuthorizeDataRecord[] = [];
        let count = 0;
        let request = `FH${stringUtil.paddingRightForString(
            params.machineId,
            15,
            ' ',
        )}${stringUtil.paddingRightForString(' ', 4, ' ')}\n`;
        for (const order of orders) {
            if (order.method == '1') {
                continue;
            }
            if (!order.shipperId) {
                logger.warn(`${order.applicationId} shipperId is null`);
                continue;
            }

            const shipperSn = order.shipperId.split('-')[1];
            const shipperType = order.shipperId.split('-')[0];
            const orderType = await this.orderService.getOderTypeByShipperType(
                shipperType,
            );
            let remark = stringUtil.paddingRightForString(shipperSn, 20, ' ');
            if (orderType) {
                remark = stringUtil.paddingRightForString(
                    `${orderType}-${shipperSn}`,
                    20,
                    ' ',
                );
            } else {
                logger.warn(`刷卡備註寫入異常，出貨單編號${order.shipperId}`);
                continue;
            }
            const uniqueOrderCode =
                moment().format('YYYYMMDD-HHmmss-') +
                stringUtil.paddingLeftForInteger(count, 4, '0');
            const data: EsunAuthorizeDataRecord = {
                FD: 'FD',
                transactionCode: '05',
                machineId: stringUtil.paddingRightForString(
                    params.machineId,
                    15,
                    ' ',
                ),
                subMachineId: stringUtil.paddingRightForString(' ', 20, ' '),
                terminalId: 'BT000001',
                orderCode: uniqueOrderCode,
                amount: stringUtil.paddingLeftForInteger(
                    order.amount * 100,
                    12,
                    '0',
                ),
                mac: stringUtil.paddingRightForString(' ', 32, ' '),
                cardNumber: order.cardNumber
                    ? stringUtil.paddingRightForString(
                          order.cardNumber,
                          19,
                          ' ',
                      )
                    : '',
                validThru: order.validThru
                    ? this.generateValidThru(order.validThru)
                    : '',
                personId: order.personId
                    ? stringUtil.paddingRightForString(order.personId, 11, ' ')
                    : stringUtil.paddingRightForString(' ', 11, ' '),
                securityCode: stringUtil.paddingRightForString(' ', 3, ' '),
                regisCode: stringUtil.paddingRightForString(' ', 4, ' '),
                bonus1: stringUtil.paddingLeftForInteger(0, 8, '0'),
                bonus2: stringUtil.paddingLeftForInteger(0, 8, '0'),
                bonus3: stringUtil.paddingLeftForInteger(0, 8, '0'),
                bonus4: stringUtil.paddingLeftForInteger(0, 8, '0'),
                bonus5: stringUtil.paddingLeftForInteger(0, 8, '0'),
                bonusCode: stringUtil.paddingRightForString(' ', 10, ' '),
                isBonus: stringUtil.paddingRightForString(' ', 1, ' '),
                respondCode: stringUtil.paddingRightForString(' ', 2, ' '),
                errorCode: stringUtil.paddingRightForString(' ', 2, ' '),
                authorizationCode: stringUtil.paddingRightForString(
                    ' ',
                    6,
                    ' ',
                ),
                descriptionEn: stringUtil.paddingRightForString(' ', 25, ' '),
                description: stringUtil.paddingRightForString('　', 20, '　'),
                remark: stringUtil.paddingRightForString(remark, 20, ' '),
            };

            const flag = this.validate(data);
            if (!flag.success) {
                //將交易結果傳更新到出貨單(刷卡失敗)
                await this.orderService.updatePaymentStatus({
                    shipperId: order.shipperId,
                    cardNumber: order.cardNumber,
                    applicationId: order.applicationId,
                    period: order.period,
                    respondCode: 'input error',
                    authorizationCode: '',
                });
                const application = await this.formService.findOne(
                    order.applicationId,
                );
                if (!application) {
                    throw new Error('application not found');
                }
                const failed =
                    await this.orderService.findOneByApplicationPeriod(
                        order.applicationId,
                        order.period,
                    );
                if (!failed) {
                    throw new Error('period of application not found');
                }
                //產生待補刷紀錄
                await this.repayService.create({
                    applicationCode: failed.applicationId,
                    shipCode: failed.shipperId,
                    period: failed.period,
                    fee: failed.amount,
                    method: failed.method,
                    storeCode: failed.storeId,
                    userCode: application?.userCode,
                    legal: failed.company,
                });

                //ASKING API
                const askingId = await this.askingService.payFailed({
                    orderType,
                    applicationId: order.applicationId,
                    period: order.period,
                    errorMessage:
                        flag.errorMessage ??
                        `卡片資料驗證失敗！請確認卡號與到期日是否正常！`,
                    storeCode: order.storeId,
                });
                logger.warn(
                    `訂單"${order.shipperId}"卡片資料驗證失敗(#${askingId})`,
                );
                continue;
            }

            const isAmericanExpress = this.checkAmericanExpress(
                order.cardNumber,
            );
            if (isAmericanExpress) {
                //將交易結果傳更新到出貨單(美國運通卡)
                await this.orderService.updatePaymentStatus({
                    shipperId: order.shipperId,
                    cardNumber: order.cardNumber,
                    applicationId: order.applicationId,
                    period: order.period,
                    respondCode: 'AMEX',
                    authorizationCode: '',
                });
                //ASKING API
                const askingId = await this.askingService.payByAmericanExpress({
                    orderType,
                    applicationId: order.applicationId,
                    period: order.period,
                    shipCode: order.shipperId,
                    isRepay: params.isRepay,
                });
                logger.warn(
                    `訂單"${order.shipperId}"，食品訂閱制，不支援美國運通卡(#${askingId})`,
                );
                continue;
            }

            authorizeData.push(data);
            const jsonStr = JSON.stringify(data);
            const json = JSON.parse(jsonStr);

            for (const key in json) {
                request += json[key];
            }

            count++;
            request += '\n';
        }

        request += `FT${stringUtil.paddingLeftForInteger(count, 6, '0')}`;

        return { request, authorizeData };
    }

    async generateFoodRequest(params: {
        subscribeDetails: ISubscribeDetail[];
        isRepay: boolean;
    }): Promise<EsunAuthorizeDataRecord[]> {
        try {
            let machineId = '';
            let shopId = '';
            let SN = '';
            switch (this.company) {
                case CLINICO:
                    machineId = configs.esun.clinico.machineId;
                    shopId = configs.esun.clinico.shopId;
                    SN = params.isRepay ? '24' : '21';
                    break;
                case SKD:
                    machineId = configs.esun.skd.machineId;
                    shopId = configs.esun.skd.shopId;
                    SN = params.isRepay ? '25' : '22';
                    break;
                case IB:
                    machineId = configs.esun.ib.machineId;
                    shopId = configs.esun.ib.shopId;
                    SN = params.isRepay ? '26' : '23';
                    break;
            }

            //授權
            const authorizeContext = await this.authorizeRequestRecord(
                {
                    machineId,
                    shopId,
                    isRepay: params.isRepay,
                },
                params.subscribeDetails,
            );
            await this.generateRequestFile(
                SN,
                shopId,
                authorizeContext.request,
            );
            return authorizeContext.authorizeData;
        } catch (err) {
            console.error(err);
            throw err;
        }
    }

    private async authorizeRequestRecord(
        params: {
            machineId: string;
            shopId: string;
            isRepay: boolean;
        },
        subscribeDetails: ISubscribeDetail[],
    ): Promise<{ request: string; authorizeData: EsunAuthorizeDataRecord[] }> {
        const authorizeData: EsunAuthorizeDataRecord[] = [];
        let count = 0;
        let request = `FH${stringUtil.paddingRightForString(
            params.machineId,
            15,
            ' ',
        )}${stringUtil.paddingRightForString(' ', 4, ' ')}\n`;

        for (const subscribeDetail of subscribeDetails) {
            if (!subscribeDetail.orderId) {
                logger.warn(`${subscribeDetail.orderId} shipperId is null`);
                continue;
            }

            const shipperSn = subscribeDetail.orderId.split('-')[1];
            const shipperType = 'FOOD';

            const remark = stringUtil.paddingRightForString(
                `${shipperType}-${shipperSn}`,
                20,
                ' ',
            );

            const uniqueOrderCode =
                moment().format('YYYYMMDD-HHmmss-') +
                stringUtil.paddingLeftForInteger(count, 4, '0');
            const data: EsunAuthorizeDataRecord = {
                FD: 'FD',
                transactionCode: '05',
                machineId: stringUtil.paddingRightForString(
                    params.machineId,
                    15,
                    ' ',
                ),
                subMachineId: stringUtil.paddingRightForString(' ', 20, ' '),
                terminalId: 'BT000001',
                orderCode: uniqueOrderCode,
                amount: stringUtil.paddingLeftForInteger(
                    subscribeDetail.expectedCardAmount * 100,
                    12,
                    '0',
                ),
                mac: stringUtil.paddingRightForString(' ', 32, ' '),
                cardNumber: subscribeDetail.creditCardNumber
                    ? stringUtil.paddingRightForString(
                          subscribeDetail.creditCardNumber,
                          19,
                          ' ',
                      )
                    : '',
                validThru: subscribeDetail.creditCardExpiredDate
                    ? this.generateValidThru(
                          subscribeDetail.creditCardExpiredDate,
                      )
                    : '',
                // personId: order.personId
                //     ? stringUtil.paddingRightForString(order.personId, 11, ' ')
                //     : stringUtil.paddingRightForString(' ', 11, ' '),
                personId: stringUtil.paddingRightForString(' ', 11, ' '),
                securityCode: stringUtil.paddingRightForString(' ', 3, ' '),
                regisCode: stringUtil.paddingRightForString(' ', 4, ' '),
                bonus1: stringUtil.paddingLeftForInteger(0, 8, '0'),
                bonus2: stringUtil.paddingLeftForInteger(0, 8, '0'),
                bonus3: stringUtil.paddingLeftForInteger(0, 8, '0'),
                bonus4: stringUtil.paddingLeftForInteger(0, 8, '0'),
                bonus5: stringUtil.paddingLeftForInteger(0, 8, '0'),
                bonusCode: stringUtil.paddingRightForString(' ', 10, ' '),
                isBonus: stringUtil.paddingRightForString(' ', 1, ' '),
                respondCode: stringUtil.paddingRightForString(' ', 2, ' '),
                errorCode: stringUtil.paddingRightForString(' ', 2, ' '),
                authorizationCode: stringUtil.paddingRightForString(
                    ' ',
                    6,
                    ' ',
                ),
                descriptionEn: stringUtil.paddingRightForString(' ', 25, ' '),
                description: stringUtil.paddingRightForString('　', 20, '　'),
                remark: stringUtil.paddingRightForString(remark, 20, ' '),
            };
            // 檢驗資訊
            const flag = this.validate(data);

            if (!flag.success) {
                console.log('error!error!');

                // 將交易結果更新明細(刷卡失敗)
                await this.orderService.updateSubscribeCardStateByOrderId(
                    subscribeDetail.orderId,
                    { isCompleted: 'N' },
                );

                //ASKING API
                const askingId = await this.askingService.foodPayFailed({
                    orderType: ShipperType.FOOD,
                    orderId: subscribeDetail.orderId,
                    period: subscribeDetail.paymentPeriod,
                    errorMessage:
                        flag.errorMessage ??
                        `卡片資料驗證失敗！請確認卡號與到期日是否正常！`,
                    storeCode: subscribeDetail.storeId,
                });
                logger.warn(
                    `出貨單"${subscribeDetail.orderId}"卡片資料驗證失敗(#${askingId})`,
                );
                continue;
            }

            const isAmericanExpress = this.checkAmericanExpress(
                data.cardNumber,
            );
            if (isAmericanExpress) {
                console.log('AE error!AE error!');

                // 將交易結果更新明細(刷卡失敗)
                await this.orderService.updateSubscribeCardStateByOrderId(
                    subscribeDetail.orderId,
                    { isCompleted: 'N' },
                );

                //ASKING API
                const askingId = await this.askingService.foodPayFailed({
                    orderType: ShipperType.FOOD,
                    orderId: subscribeDetail.orderId,
                    period: subscribeDetail.paymentPeriod,
                    errorMessage:
                        'VPOS不支援美國運通卡\n若本次交易更換其他發卡機構，也煩請透過手動方式補刷',
                    storeCode: subscribeDetail.storeId,
                });
                logger.warn(
                    `出貨單"${subscribeDetail.orderId}"使用美國運通卡，需手動補刷(#${askingId})`,
                );
                continue;
            }

            authorizeData.push(data);
            const jsonStr = JSON.stringify(data);
            const json = JSON.parse(jsonStr);

            for (const key in json) {
                request += json[key];
            }

            await this.orderService.updateSubscribeCardStateByOrderId(
                subscribeDetail.orderId,
                { isProcessed: 'Y' },
            );

            count++;
            request += '\n';
        }
        request += `FT${stringUtil.paddingLeftForInteger(count, 6, '0')}`;

        return { request, authorizeData };
    }

    private async generateRequestFile(
        SN: string,
        shopId: string,
        context: string,
    ): Promise<void> {
        const folderPath = `${configs.esun.uploadFolder}/${this.company}`;
        if (!fs.existsSync(folderPath)) {
            mkdirp.sync(folderPath);
        }
        const ext = '.TXT';
        const fileName = `${shopId}#${moment().format('YYYYMMDD')}${SN}I${ext}`;
        const filePath = `${folderPath}/${fileName}`;
        await this.createFile(filePath, context);
    }

    private generateValidThru(validThru: string): string {
        if (validThru.length !== 4) return '';
        const mm = validThru.substring(0, 2);
        const yy = validThru.substring(2, 4);
        return yy + mm;
    }

    private validate(input: EsunAuthorizeDataRecord): {
        success: boolean;
        errorMessage?: string;
    } {
        if (!input.machineId) {
            return {
                success: false,
                errorMessage: 'machine id failed',
            };
        }
        const cardNumberRegExp = new RegExp(/^\d{13,19}$/);
        if (
            !input.cardNumber ||
            input.cardNumber.trim().length == 0 ||
            !cardNumberRegExp.test(input.cardNumber.trim())
        ) {
            return {
                success: false,
                errorMessage: '信用卡卡號格式錯誤',
            };
        }
        const validThruRegExp = new RegExp(/^\d{4}$/);
        if (
            !input.validThru ||
            input.validThru.length == 0 ||
            !validThruRegExp.test(input.validThru)
        ) {
            return {
                success: false,
                errorMessage: '信用卡有效日期格式錯誤',
            };
        }
        return { success: true };
    }

    private checkAmericanExpress(cardNumber: string): boolean {
        const reg = new RegExp(/^(37|34)\d{13}$/);
        return reg.test(cardNumber);
    }

    private async createFile(
        filePath: string,
        context: string,
    ): Promise<boolean> {
        return new Promise((resolve, reject) => {
            const file = fs.createWriteStream(filePath);
            const buffer = iconv.encode(context, 'big5');
            file.write(buffer);
            file.end();
            file.on('finish', () => {
                resolve(true);
            });
            file.on('error', reject);
        });
    }

    async analyzeFile(params: { shipDate?: Date; isRepay: boolean }) {
        const filesName: string[] = [];
        // const data: EsunAuthorizeRespondData[] = [];
        const authorizeDataRecord: eSunSubscribeDetailData[] = [];
        const shipDate = params.shipDate
            ? moment(params.shipDate).toDate()
            : moment().toDate();
        let shopId = '';
        switch (this.company) {
            case CLINICO:
                shopId = configs.esun.clinico.shopId;
                break;
            case SKD:
                shopId = configs.esun.skd.shopId;
                break;
            case IB:
                shopId = configs.esun.ib.shopId;
                break;
        }
        const downloadFolder = `${configs.esun.downloadFolder}/${this.company}`;
        const filePaths = fileUtil.getFilePaths(downloadFolder);

        const reg = RegExp(
            `^${shopId}\\$${moment(shipDate).format('YYYYMMDD')}\\d{2}`,
        );

        for (const filePath of filePaths) {
            const check = await fileUtil.checkFile(filePath);
            if (!check) {
                logger.warn(`[${this.company}] File not found! (${filePath})`);
                continue;
            }
            const name = path.basename(filePath);
            if (!reg.test(name)) {
                console.log('continue', name);
                await Helpers.File.move(
                    filePath,
                    `${configs.esun.downloadBackUpFolder}/${this.company}-${name}`,
                    true,
                );
                continue;
            }
            const extname = path.extname(filePath);
            const stream = fs.createReadStream(filePath);
            const rl = readline.createInterface({
                input: stream.pipe(iconv.decodeStream('big5')),
            });

            for await (const line of rl) {
                if (line.substr(0, 2) != 'FD') {
                    continue;
                }

                console.log(`line length: ${line.length}`);

                if (line.length != 301) {
                    logger.warn(`玉山授權回覆檔案內容異常: ${line}`);
                    continue;
                }

                const authorizeRespondDataRecord: EsunAuthorizeRespondDataRecord =
                    {
                        FD: line.substr(0, 2),
                        transactionCode: line.substr(2, 2),
                        machineId: line.substr(4, 15),
                        subMachineId: line.substr(19, 20),
                        terminalId: line.substr(39, 8),
                        orderCode: line.substr(47, 20),
                        amount: line.substr(67, 12),
                        mac: line.substr(79, 32),
                        cardNumber: line.substr(111, 19),
                        validThru: line.substr(130, 4),
                        personId: line.substr(134, 11),
                        securityCode: line.substr(145, 3),
                        regisCode: line.substr(148, 4),
                        bonus1: line.substr(152, 8),
                        bonus2: line.substr(160, 8),
                        bonus3: line.substr(168, 8),
                        bonus4: line.substr(176, 8),
                        bonus5: line.substr(184, 8),
                        bonusCode: line.substr(192, 10),
                        isBonus: line.substr(202, 1),
                        respondCode: line.substr(203, 2),
                        errorCode: line.substr(205, 2),
                        authorizationCode: line.substr(207, 6),
                        descriptionEn: line.substr(213, 25),
                        description: line.substr(238, 20),
                        successCardNumber: line.substr(258, 19),
                        successValidThru: line.substr(277, 4),
                        remark: line.substr(281, 20),
                    };
                let errorCode = '';
                const isEmptyErrorCode = Helpers.Str.isEmpty(
                    authorizeRespondDataRecord.errorCode.trim(),
                );
                if (isEmptyErrorCode) {
                    errorCode = authorizeRespondDataRecord.respondCode.trim();
                } else {
                    errorCode = authorizeRespondDataRecord.errorCode.trim();
                }
                const respondMessage = this.getRespondMessage(errorCode);

                const remarkOrderId = authorizeRespondDataRecord.remark.trim();
                const orderId = remarkOrderId.split('-')[1];
                const shipperType = remarkOrderId.split('-')[0];

                // 透過 remark 去區分 助聽器訂閱 或是 保健食品訂閱
                if (shipperType !== 'FOOD') {
                    logger.warn(`刷卡備註異常，食品訂單編號${orderId}`);
                    continue;
                }

                // 找出待刷訂單 （申請書出貨日前10天要觸發刷卡）
                const subscribeDetail =
                    await this.orderService.getOneSubscribeDetailByOrderID(
                        orderId,
                    );
                if (!subscribeDetail) {
                    logger.warn(`查無此訂單號: ${orderId}`);
                    continue;
                }

                authorizeDataRecord.push({
                    ...subscribeDetail,
                    cardNumber: authorizeRespondDataRecord.cardNumber,
                    responseMessage: respondMessage,
                    respondCode: authorizeRespondDataRecord.respondCode,
                });
            }
            filesName.push(name);
        }
        return {
            filesName,
            authorizeDataRecord,
        };
    }

    async generateResponse(params: {
        shipDate?: Date;
        isRepay: boolean;
    }): Promise<IGenerateAuthorizeResponseResult> {
        const filesName: string[] = [];
        const data: EsunAuthorizeRespondData[] = [];
        const authorizeDataRecord: EsunAuthorizeRespondDataRecord[] = [];
        const shipDate = params.shipDate
            ? moment(params.shipDate).toDate()
            : moment().toDate();
        let shopId = '';
        switch (this.company) {
            case CLINICO:
                shopId = configs.esun.clinico.shopId;
                break;
            case SKD:
                shopId = configs.esun.skd.shopId;
                break;
            case IB:
                shopId = configs.esun.ib.shopId;
                break;
        }
        const downloadFolder = `${configs.esun.downloadFolder}/${this.company}`;
        const filePaths = fileUtil.getFilePaths(downloadFolder);

        const reg = RegExp(
            `^${shopId}\\$${moment(shipDate).format('YYYYMMDD')}\\d{2}`,
        );

        for (const filePath of filePaths) {
            const check = await fileUtil.checkFile(filePath);
            if (!check) {
                logger.warn(`[${this.company}] File not found! (${filePath})`);
                continue;
            }
            const name = path.basename(filePath);
            if (!reg.test(name)) {
                console.log('continue', name);
                await Helpers.File.move(
                    filePath,
                    `${configs.esun.downloadBackUpFolder}/${this.company}-${name}`,
                    true,
                );
                continue;
            }
            const extname = path.extname(filePath);
            const stream = fs.createReadStream(filePath);
            const rl = readline.createInterface({
                input: stream.pipe(iconv.decodeStream('big5')),
            });

            for await (const line of rl) {
                if (line.substr(0, 2) != 'FD') {
                    continue;
                }

                if (line.length != 301) {
                    logger.warn(`玉山授權回覆檔案內容異常: ${line}`);
                    continue;
                }

                const authorizeRespondDataRecord: EsunAuthorizeRespondDataRecord =
                    {
                        FD: line.substr(0, 2),
                        transactionCode: line.substr(2, 2),
                        machineId: line.substr(4, 15),
                        subMachineId: line.substr(19, 20),
                        terminalId: line.substr(39, 8),
                        orderCode: line.substr(47, 20),
                        amount: line.substr(67, 12),
                        mac: line.substr(79, 32),
                        cardNumber: line.substr(111, 19),
                        validThru: line.substr(130, 4),
                        personId: line.substr(134, 11),
                        securityCode: line.substr(145, 3),
                        regisCode: line.substr(148, 4),
                        bonus1: line.substr(152, 8),
                        bonus2: line.substr(160, 8),
                        bonus3: line.substr(168, 8),
                        bonus4: line.substr(176, 8),
                        bonus5: line.substr(184, 8),
                        bonusCode: line.substr(192, 10),
                        isBonus: line.substr(202, 1),
                        respondCode: line.substr(203, 2),
                        errorCode: line.substr(205, 2),
                        authorizationCode: line.substr(207, 6),
                        descriptionEn: line.substr(213, 25),
                        description: line.substr(238, 20),
                        successCardNumber: line.substr(258, 19),
                        successValidThru: line.substr(277, 4),
                        remark: line.substr(281, 20),
                    };
                authorizeDataRecord.push(authorizeRespondDataRecord);

                const shipperId = authorizeRespondDataRecord.remark.trim();
                const shipperSn = shipperId.split('-')[1];
                const shipperType = shipperId.split('-')[0];
                const orderType = await this.orderService.getOderTypeHearder(
                    shipperType,
                );
                if (!orderType) {
                    logger.warn(`刷卡備註異常，出貨單編號${shipperId}`);
                    continue;
                }

                const order = await this.getFeeDetail({
                    shipDate,
                    shipId: `${orderType.shipPrefixCode}-${shipperSn}`,
                    isRepay: params.isRepay,
                });

                if (!order) {
                    continue;
                }

                let errorCode = '';
                const isEmptyErrorCode = Helpers.Str.isEmpty(
                    authorizeRespondDataRecord.errorCode.trim(),
                );
                if (isEmptyErrorCode) {
                    errorCode = authorizeRespondDataRecord.respondCode.trim();
                } else {
                    errorCode = authorizeRespondDataRecord.errorCode.trim();
                }

                const respondMessage = this.getRespondMessage(errorCode);
                data.push({
                    orderType: orderType.type,
                    shipperId: order.shipperId,
                    cardNumber: order.cardNumber,
                    applicationId: order.applicationId,
                    period: order.period,
                    respondCode: authorizeRespondDataRecord.respondCode.trim(),
                    errorCode: authorizeRespondDataRecord.errorCode.trim(),
                    respondMessage: respondMessage,
                    authorizationCode:
                        authorizeRespondDataRecord.authorizationCode,
                    storeId: order.storeId,
                    amount: order.amount,
                });
            }

            filesName.push(name);
        }

        return {
            filesName,
            data,
        };
    }

    private async getFeeDetail(params: {
        shipDate: Date;
        shipId: string;
        isRepay: boolean;
    }): Promise<Order | null> {
        if (params.isRepay) {
            return await this.repayService.findOneOrNull({
                feeDate: params.shipDate,
                shipId: params.shipId,
            });
        } else {
            return await this.orderService.findOneOrNull({
                date: moment(params.shipDate).format('YYMMDD'),
                shipId: params.shipId,
            });
        }
    }

    private getRespondMessage(code: string): string {
        let message = '';
        switch (code) {
            case '00':
                message = `(${code})核准`;
                break;
            case '01':
            case '02':
                message = `(${code})卡片餘額不足，請客人聯繫發卡行`;
                break;
            case '14':
                message = `(${code})無效卡號，請客人換卡訂閱`;
                break;
            case '31':
                message = `(${code})此卡未開通線上刷卡，請客人換卡訂閱`;
                break;
            case '33':
            case '54':
                message = `(${code})卡片過期，請客人換卡訂閱`;
                break;
            case '62':
                message = `(${code})尚未開卡`;
                break;
            case 'L1':
                message = `(${code})產品代碼錯誤`;
                break;
            case 'L2':
                message = `(${code})期數錯誤`;
                break;
            case 'L3':
                message = `(${code})不支援分期(他行卡)`;
                break;
            case 'L4':
                message = `(${code})產品代碼過期`;
                break;
            case 'L5':
                message = `(${code})金額無效`;
                break;
            case 'L6':
                message = `(${code})不支援分期`;
                break;
            case 'L7':
                message = `(${code})非限定卡別交易`;
                break;
            case 'XA':
                message = `(${code})紅利自付額有誤`;
                break;
            case 'XB':
                message = `(${code})紅利商品數量有誤`;
                break;
            case 'XC':
                message = `(${code})紅利商品數量超過可折抵上限`;
                break;
            case 'XD':
                message = `(${code})紅利商品折抵點數超過最高折`;
                break;
            case 'XE':
                message = `(${code})紅利商品傳入之固定點數有誤`;
                break;
            case 'XF':
                message = `(${code})紅利折抵金額超過消費金額`;
                break;
            case 'X1':
                message = `(${code})不允許使用紅利折抵現金功能`;
                break;
            case 'X2':
                message = `(${code})點數未達可折抵點數下限`;
                break;
            case 'X3':
                message = `(${code})他行卡不支援紅利折抵`;
                break;
            case 'X4':
                message = `(${code})此活動已逾期`;
                break;
            case 'X5':
                message = `(${code})金額未超過限額不允許使用`;
                break;
            case 'X6':
                message = `(${code})特店不允許紅利交易`;
                break;
            case 'X7':
                message = `(${code})點數不足`;
                break;
            case 'X8':
                message = `(${code})非正卡持卡人`;
                break;
            case 'X9':
                message = `(${code})紅利商品編號有誤或空白`;
                break;
            case 'ET':
                message = `(${code})銀聯卡自訂回覆碼`;
                break;
            case 'UE':
                message = `(${code})請聯繫收單銀行`;
                break;
            case 'TE':
                message = `(${code})Token 格式錯誤`;
                break;
            case 'TI':
                message = `(${code})無效 Token`;
                break;
            case 'G0':
                message = `(${code})系統功能有誤`;
                break;
            case 'G1':
                message = `(${code})交易逾時`;
                break;
            case 'G2':
                message = `(${code})資料格式錯誤`;
                break;
            case 'G3':
                message = `(${code})非使用中特店`;
                break;
            case 'G4':
                message = `(${code})特店交易類型不合`;
                break;
            case 'G5':
                message = `(${code})連線 IP 不合`;
                break;
            case 'G6':
                message = `(${code})訂單編號重複`;
                break;
            case 'G7':
                message = `(${code})使用未定義之紅利點數進行交易`;
                break;
            case 'G8':
                message = `(${code})押碼錯誤`;
                break;
            case 'G9':
                message = `(${code})Session 檢查有誤`;
                break;
            case 'GA':
                message = `(${code})無效的持卡人資料`;
                break;
            case 'GB':
                message = `(${code})不允許執行授權取消交易`;
                break;
            case 'GC':
                message = `(${code})退貨期限逾期`;
                break;
            case 'GD':
                message = `(${code})查無訂單編號`;
                break;
            case 'GE':
                message = `(${code})查無交易明細`;
                break;
            case 'GF':
                message = `(${code})交易資料狀態不符`;
                break;
            case 'GG':
                message = `(${code})交易失敗`;
                break;
            case 'GH':
                message = `(${code})訂單編號重複送出交易`;
                break;
            case 'GI':
                message = `(${code})銀行紅利狀態不符`;
                break;
            case 'GJ':
                message = `(${code})出團日期不合法`;
                break;
            case 'GK':
                message = `(${code})延後出團天數超過限定天數`;
                break;
            case 'GL':
                message = `(${code})非限定特店，不可使用「玉山卡」參數`;
                break;
            case 'GM':
                message = `(${code})限定特店，必須傳送「玉山卡」參數`;
                break;
            case 'GN':
                message = `(${code})該卡號非玉山卡所屬`;
                break;
            case 'GP':
                message = `(${code})銀行紅利與分期只能二選一`;
                break;
            case 'GR':
                message = `(${code})使用者取消刷卡頁面`;
                break;
            case 'GS':
                message = `(${code})系統暫停服務`;
                break;
            case 'GT':
                message = `(${code})交易時間逾時`;
                break;
            case 'GU':
                message = `(${code})預先授權重覆交易`;
                break;
            case 'GV':
                message = `(${code})無預先授權成功交易紀錄`;
                break;
            case 'GW':
                message = `(${code})無預先授權交易紀錄`;
                break;
            case 'GX':
                message = `(${code})3D 交易異常`;
                break;
            case 'GY':
                message = `(${code})3D 交易異常`;
                break;
            case 'GZ':
                message = `(${code})3D 交易異常`;
                break;
            case 'V0':
                message = `(${code})3D 驗證失敗`;
                break;
            case 'V1':
                message = `(${code})3D 交易異常`;
                break;
            case 'V2':
                message = `(${code})發卡行(ACS)系統異常`;
                break;
            case 'VA':
                message = `(${code})取消交易金額有誤`;
                break;
            case 'VD':
                message = `(${code})取消交易時間不合法(日期)`;
                break;
            case 'VT':
                message = `(${code})取消交易時間不合法(時間)`;
                break;
            case 'RA':
                message = `(${code})退貨交易金額有誤`;
                break;
            case 'RD':
                message = `(${code})超過退貨交易期限`;
                break;
            case 'RP':
                message = `(${code})退貨交易不符`;
                break;
            case 'N1':
                message = `(${code})紅利點數與金額不符`;
                break;
            case 'NM':
                message = `(${code})未設定特店類型或英文名稱`;
                break;
            case 'NR':
                message = `(${code})不允許退貨交易`;
                break;
            case 'NT':
                message = `(${code})未設定為銀聯卡交易特店`;
                break;
            case 'NV':
                message = `(${code})不允許取消交易`;
                break;
            case 'Z0':
                message = `(${code})URL 錯誤`;
                break;
            case 'Z1':
                message = `(${code})不允許銀聯交易`;
                break;
            case 'Z2':
                message = `(${code})無法進行完全 3D 交易`;
                break;
            case 'QQ':
                message = `(${code})不允許 Debit Card 交易`;
                break;
            case '51':
                message = `(${code})簽帳金融卡帳戶餘額不足`;
                break;
            //(1G)、(C3)、(C5)、(E1)、(E3)、(DT)、(04)、(05) 、(06)、(07)、(12)、(13)、(15)、(20)、(21)、(23)、(25)、(34)、(35)、(36)、(57)、(66)、(65)、(78)
            default:
                message = `(${code})拒絕交易原因僅有發卡銀行才會知道，建議客人可向發卡行查詢。或請改以其他信用卡訂閱。`;
                break;
        }
        return message;
    }

    async execUpload(params?: { isRequire: boolean }) {
        try {
            let uploadPath = `${configs.esun.uploadFolder}/${this.company}`;
            if (params?.isRequire) {
                uploadPath += '/require';
            }
            if (!fs.existsSync(uploadPath)) {
                mkdirp.sync(uploadPath);
            }

            const logPath = `${configs.log.path}/upload/`;
            const logFileName = `${this.company}-esun-${moment().format(
                'YYYYMMDD',
            )}.log`;
            if (!fs.existsSync(logPath)) {
                mkdirp.sync(logPath);
            }

            const { machineId, account, password } = this.getAccountInfo();

            const cmd = `java -classpath ${
                configs.esun.jarPath
            } aptransfile.AutoUpload -m ${machineId} -u ${account} -p ${password} -l ${
                logPath + logFileName
            } -d ${uploadPath}`;

            const output = shelljs.exec(cmd, async (code, stdout, stderr) => {
                console.log('Exit code:', code);
                console.log('Program output:', stdout);
                console.log('Program stderr:', stderr);
                //process closed then load response files
                this.emitter.emit('jar_upload_closed', {
                    code,
                    stdout,
                    stderr,
                });
            });
        } catch (err) {
            console.error(err);
        }
    }

    async execDownload(params: { isRepay: boolean }) {
        try {
            const downloadFolder = `${configs.esun.downloadFolder}/${this.company}`;
            if (!fs.existsSync(downloadFolder)) {
                mkdirp.sync(downloadFolder);
            }

            const logPath = `${configs.log.path}/download/`;
            const logFileName = `${this.company}-esun-${moment().format(
                'YYYYMMDD',
            )}.log`;
            if (!fs.existsSync(logPath)) {
                mkdirp.sync(logPath);
            }

            const { machineId, account, password } = this.getAccountInfo();

            const cmd = `java -classpath ${
                configs.esun.jarPath
            } aptransfile.Download -m ${machineId} -u ${account} -p ${password} -l ${
                logPath + logFileName
            } -d ${downloadFolder}`;

            const output = shelljs.exec(cmd, async (code, stdout, stderr) => {
                console.log('Exit code:', code);
                console.log('Program output:', stdout);
                console.log('Program stderr:', stderr);
                //process closed then load response files
                if (params.isRepay) {
                    this.emitter.emit('jar_repay_download_closed', {
                        code,
                        stdout,
                        stderr,
                    });
                } else {
                    this.emitter.emit('jar_download_closed', {
                        code,
                        stdout,
                        stderr,
                    });
                }
            });
        } catch (err) {
            console.error(err);
        }
    }

    private getAccountInfo(): {
        machineId: string;
        account: string;
        password: string;
    } {
        let machineId = '';
        let account = '';
        let password = '';
        switch (this.company) {
            case CLINICO:
                machineId = configs.esun.clinico.machineId;
                account = configs.esun.clinico.account;
                password = configs.esun.clinico.password;
                break;
            case SKD:
                machineId = configs.esun.skd.machineId;
                account = configs.esun.skd.account;
                password = configs.esun.skd.password;
                break;
            case IB:
                machineId = configs.esun.ib.machineId;
                account = configs.esun.ib.account;
                password = configs.esun.ib.password;
                break;
        }

        return {
            machineId,
            account,
            password,
        };
    }
}
