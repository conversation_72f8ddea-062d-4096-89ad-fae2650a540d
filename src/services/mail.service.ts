import { Utils } from '@clinico/clinico-node-framework';
import { CLINICO, IB, SKD } from '../const';
import { ISendACHMailParams } from './interfaces/mail.interface';
import * as moment from 'moment';
import configs from '../configs';

export class MailService {
    private company: string;

    constructor(company: string) {
        this.company = company;
    }

    async sendACHMail(params: ISendACHMailParams): Promise<boolean> {
        try {
            let companyName = '';
            const to: string[] = [
                //主辦:
                '<EMAIL>', //李明萱21678
                '<EMAIL>',
                '<EMAIL>', //周小姐(代理)
                '<EMAIL>', //詹鈞皓22739
            ];
            if (configs.bank.serviceMail) {
                to.push(configs.bank.serviceMail);
            }
            const cc: string[] = [
                //主管:
                '<EMAIL>', //陳品蓁10079
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
            ];
            switch (this.company) {
                // HA Lily Lee[李采凌] L1719 <EMAIL>,
                case CLINICO: {
                    companyName = '科林國際助聽器股份有限公司';
                    cc.push('<EMAIL>');
                    break;
                }
                // SKD Jenny Yang[楊卉溱] L1700 <EMAIL>
                case SKD:
                    companyName = '濰樂聽力股份有限公司';
                    cc.push('<EMAIL>');
                    break;
                // IB Christina Hu[胡維庭] L1853 <EMAIL>
                case IB:
                    companyName = '聽貝爾股份有限公司';
                    cc.push('<EMAIL>');
                    break;
            }

            const ACHDAte = params.ACHDate
                ? `[應扣款日${moment(params.ACHDate).format('YYYY-MM-DD')}]`
                : '';
            const repayACHDAte = params.repayACHDate
                ? `[補扣款日${moment(params.repayACHDate).format(
                      'YYYY-MM-DD',
                  )}]`
                : '';
            const subject = `[${companyName}][ACH入扣帳資料提出檔]${ACHDAte}${repayACHDAte} ${moment().format(
                'YYYY-MM-DD',
            )}`;

            // send mail
            const mailResult = await Utils.Mailer.send({
                to:
                    process.env.NODE_ENV === 'production'
                        ? to.toString()
                        : ['<EMAIL>'].toString(),
                cc:
                    process.env.NODE_ENV === 'production'
                        ? cc.toString()
                        : null,
                subject:
                    (process.env.NODE_ENV === 'production' ? '' : '[TEST]') +
                    subject,
                body: '您好：\r 附件為今日的扣款資料，再麻煩協助送件，謝謝',
                filePaths: params.filePaths,
            });

            return mailResult;
        } catch (err) {
            throw err;
        }
    }
}
