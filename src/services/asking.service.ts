import db from '@clinico/clinico-persistence';
import { Utils } from '@clinico/clinico-node-framework';
import logger from './logger.service';
import {
    IPublishAskingParams,
    IFailedParams,
    IPayFailed,
    IRepayCompletedParams,
    IRepayFailedParams,
    IUpdateAskingParams,
    IAskingDescriptionParams,
    IInvoiceFailedParams,
    IPayByAmericanExpress,
    IAskingACHDescriptionParams,
    IACHFailedParams,
    IReplyParams,
    IFoodAskingDescriptionParams,
} from './interfaces/asking.interface';
import { FormService } from './form.service';
import { ShipperType } from './interfaces/order.interface';
import { RepayService } from './repay.service';
import { OrderService } from './order.service';
import * as moment from 'moment';
import { managers, CLINICO, SKD, IB } from '../const';
import stringUtil from '../utils/string';
import { SOAPService } from './soap.service';
import * as ip from 'ip';
import { EnumPayFailedType } from './interfaces/repay.interface';

export class AskingService {
    private company: string;
    private formService: FormService;
    private repayService: RepayService;
    private orderService: OrderService;
    private soapService = new SOAPService();

    constructor(company: string) {
        this.company = company;
        this.formService = new FormService(this.company);
        this.repayService = new RepayService(this.company);
        this.orderService = new OrderService(this.company);
    }

    private async publish(
        params: IPublishAskingParams,
    ): Promise<number | undefined> {
        try {
            const project = await Utils.Redmine.Project.findOneByName(
                '訂閱制追蹤',
            );
            if (!project.success) {
                logger.warn('訂閱制追蹤project不存在');
                return;
            }
            let description = '';
            if (typeof params.descriptionInput == 'string') {
                description = params.descriptionInput;
            } else {
                description = this.getDescription(params.descriptionInput);
            }

            const issue = await Utils.Redmine.Issue.create({
                project_id: project.data!.id,
                tracker_id: 38,
                status_id: Utils.Redmine.Issue.IssueStatusId.Open,
                priority_id: Utils.Redmine.Issue.IssuePriorityId.Urgent,
                subject: params.subject,
                description,
                assigned_to_id: params.assignedId,
                watcher_user_ids: params.watcherIds,
                is_private: false,
                parent_issue_id: params.parentAskingId,
            });

            if (!issue.success) {
                logger.error(
                    `Asking建立失敗(${params.subject}): ${issue.errors?.join(
                        '\n',
                    )}`,
                );
                return;
            }
            return issue.data?.id;
        } catch (err) {
            logger.error(`Asking建立失敗(${params.subject})`);
            throw err;
        }
    }

    private getDescription(
        descriptionInput:
            | IAskingDescriptionParams
            | IAskingACHDescriptionParams
            | IFoodAskingDescriptionParams,
    ): string {
        if ('orderId' in descriptionInput) {
            return this.generateFoodDescriptionTemplate(descriptionInput);
        } else if ('cardNumber' in descriptionInput) {
            return this.generateDescriptionTemplate(descriptionInput);
        } else if ('bankCode' in descriptionInput) {
            return this.generateACHDescriptionTemplate(descriptionInput);
        }
        return '';
    }

    private async update(params: IUpdateAskingParams): Promise<void> {
        try {
            const res = await Utils.Redmine.Issue.findOne(params.askingId);
            const issue = res.data!;
            const updateIssue = await Utils.Redmine.Issue.update({
                id: issue.id,
                notes: params.notes,
                project_id: issue.project.id,
                tracker_id: issue.tracker!.id,
                status_id: params.isClosed ? 3 : issue.status.id,
                priority_id: issue.priority!.id,
                subject: issue.subject,
                description: issue.description,
                assigned_to_id: issue.assigned_to?.id,
                watcher_user_ids: issue.watchers?.map((user) => user.id),
                is_private: issue.is_private,
                done_ratio: params.isClosed
                    ? 100
                    : issue.done_ratio ?? undefined,
                due_date: params.isClosed
                    ? moment().format('YYYY-MM-DD')
                    : issue.due_date ?? undefined,
            });
            if (!updateIssue.success) {
                logger.warn(`Asking更新失敗(#${params.askingId})`);
            }
        } catch (err) {
            logger.error(`Asking更新失敗(#${params.askingId})`);
            throw err;
        }
    }

    async replyProcessACH(params: IReplyParams) {
        try {
            const failed = await this.repayService.findOneOrNullForProcessACH({
                applicationId: params.applicationId,
                period: params.period,
            });
            if (!failed) {
                throw new Error(
                    `RS_FEE_REPAY not found(${params.applicationId},${params.period})`,
                );
            }
            await this.update({
                askingId: failed.askingId,
                notes: `
                系統已產生補扣款交易，
                預計扣款日為 ${params.achDate}`,
            });
        } catch (err) {
            logger.error(`update asking failed error: ${err}`);
        }
    }

    /**
     * 處理信用卡付款失敗的主要流程
     * @param params - 付款失敗參數
     * @returns Asking ID
     */
    async payFailed(params: IPayFailed): Promise<number | undefined> {
        try {
            // 1. 取得並驗證基本資料
            const basicData = await this.getBasicDataForPaymentFailure(params);

            // 2. 判斷失敗類型並建立對應的 Asking 案件
            const askingId = await this.createAskingByFailureType(basicData);

            // 3. 更新 Asking ID 到補刷記錄
            await this.updateRepayAskingId(params, askingId);

            return askingId;
        } catch (err) {
            logger.error(
                `payFailed() error(${params.applicationId}, ${params.period}): ${err}`,
            );
        }
    }

    /**
     * 取得付款失敗所需的基本資料
     * @param params - 付款失敗參數
     * @returns 包含所有必要資料的物件
     */
    private async getBasicDataForPaymentFailure(params: IPayFailed) {
        const application = await this.formService.findOne(
            params.applicationId,
        );
        if (!application) {
            throw new Error('application not found');
        }

        const failed = await this.orderService.findOneByApplicationPeriod(
            params.applicationId,
            params.period,
        );
        if (!failed) {
            throw new Error('period of application not found');
        }

        const [member, user, store, payFailedType] = await Promise.all([
            db.Member.findOne({
                where: { clientCode: application.memberCode },
            }),
            db.User.findOne({ where: { code: application.userCode } }),
            db.Store.findOne({ where: { code: failed.storeId } }),
            this.repayService.getPayFailedType(application.applicationCode),
        ]);

        // 檢查會員手機號碼
        const mobilePhone = member?.cellPhone;
        const noMobilePhoneMessage = !mobilePhone
            ? `\n\n⚠️ 注意：會員資訊沒有手機號碼，無法發送簡訊通知。`
            : '';

        return {
            application,
            failed,
            member,
            user,
            store,
            payFailedType,
            input: {
                orderType: params.orderType,
                applicationId: params.applicationId,
                orderCode: application.orderCode,
                shipCode: failed.shipperId,
                period: params.period,
                memberCode: application.memberCode,
                memberName: member?.name ?? '',
                userCode: application.userCode,
                userName: user?.name ?? '',
                errorMessage: params.errorMessage + noMobilePhoneMessage,
                storeCode: params.storeCode,
                storeName: store?.name ?? '',
                cardNumber: this.maskCardNaumber(application.cardNumber),
            },
        };
    }

    /**
     * 根據失敗類型建立對應的 Asking 案件
     * @param basicData - 基本資料
     * @returns Asking ID
     */
    private async createAskingByFailureType(basicData: any): Promise<number> {
        const { payFailedType, input } = basicData;

        let askingId: number | undefined;

        switch (payFailedType) {
            case EnumPayFailedType.Normal:
            case EnumPayFailedType.ConsecutivePeriods2:
                askingId = await this.failed(input);
                break;
            case EnumPayFailedType.ConsecutivePeriods3:
                askingId = await this.repayFailed3ConsecutivePeriods(input);
                break;
            case EnumPayFailedType.ConsecutivePeriods4:
            default:
                askingId = await this.repayFailed4ConsecutivePeriods(input);
                break;
        }

        if (!askingId) {
            throw new Error('askingId is undefined');
        }

        return askingId;
    }

    /**
     * 更新補刷記錄的 Asking ID
     * @param params - 原始參數
     * @param askingId - Asking ID
     */
    private async updateRepayAskingId(
        params: IPayFailed,
        askingId: number,
    ): Promise<void> {
        await this.repayService.updateAskingId({
            filters: {
                applicationCode: params.applicationId,
                period: params.period,
            },
            askingId,
        });
    }

    /**
     * 處理 ACH 付款失敗的主要流程
     * @param params - 付款失敗參數
     * @returns Asking ID
     */
    async payFailedForACH(params: IPayFailed): Promise<number | undefined> {
        try {
            // 1. 取得基本資料
            const basicData = await this.getBasicDataForACHFailure(params);

            // 2. 建立 ACH 失敗 Asking 案件
            return await this.ACHFailed(basicData.input);
        } catch (err) {
            logger.error(
                `payFailedForACH() error(${params.applicationId}, ${params.period}): ${err}`,
            );
        }
    }

    /**
     * 取得 ACH 付款失敗所需的基本資料
     * @param params - 付款失敗參數
     * @returns 包含所有必要資料的物件
     */
    private async getBasicDataForACHFailure(params: IPayFailed) {
        const application = await this.formService.findOne(
            params.applicationId,
        );
        if (!application) {
            throw new Error('application not found');
        }

        const failed = await this.orderService.findOneByApplicationPeriod(
            params.applicationId,
            params.period,
        );
        if (!failed) {
            throw new Error('period of application not found');
        }

        const [member, user, store] = await Promise.all([
            db.Member.findOne({
                where: { clientCode: application.memberCode },
            }),
            db.User.findOne({ where: { code: application.userCode } }),
            db.Store.findOne({ where: { code: failed.storeId } }),
        ]);

        // 檢查會員手機號碼
        const mobilePhone = member?.cellPhone;
        const noMobilePhoneMessage = !mobilePhone
            ? `\n\n⚠️ 注意：會員 ${application.memberCode} 沒有手機號碼，無法發送簡訊通知。`
            : '';

        return {
            application,
            failed,
            member,
            user,
            store,
            input: {
                orderType: params.orderType,
                applicationId: params.applicationId,
                orderCode: application.orderCode,
                shipCode: failed.shipperId,
                period: params.period,
                memberCode: application.memberCode,
                memberName: member?.name ?? '',
                userCode: application.userCode,
                userName: user?.name ?? '',
                errorMessage: params.errorMessage + noMobilePhoneMessage,
                storeCode: params.storeCode,
                storeName: store?.name ?? '',
                bankCode: failed.bankCode,
                bankAccount: this.maskBankAccount(failed.bankAccount),
            },
        };
    }

    async payByAmericanExpress(
        params: IPayByAmericanExpress,
    ): Promise<number | null> {
        try {
            const application = await this.formService.findOne(
                params.applicationId,
            );
            if (!application) {
                throw new Error('application not found');
            }

            let parentAskingId: number | undefined;
            if (params.isRepay) {
                const failed = await this.repayService.findOneOrNull({
                    applicationId: params.applicationId,
                    period: params.period,
                });
                if (failed) {
                    parentAskingId = failed.askingId;
                }
            }

            const member = await db.Member.findOne({
                where: { clientCode: application.memberCode },
            });

            const user = await db.User.findOne({
                where: { code: application.userCode },
            });

            const store = await db.Store.findOne({
                where: { code: application.storeCode },
            });

            const input = {
                orderType: params.orderType,
                applicationId: params.applicationId,
                orderCode: application.orderCode,
                shipCode: params.shipCode,
                period: params.period,
                memberCode: application.memberCode,
                memberName: member?.name ?? '',
                userCode: application.userCode,
                userName: user?.name ?? '',
                errorMessage:
                    'VPOS不支援美國運通卡\n若本次交易更換其他發卡機構，也煩請透過手動方式補刷',
                storeCode: application.storeCode,
                storeName: store?.name ?? '',
                cardNumber: this.maskCardNaumber(application.cardNumber),
                parentAskingId,
            };

            //財務人員
            const { assigned, watchers } =
                await this.getFINsForAmericanExpress();

            //運通卡財務手動補刷完後，需要透過人工方式將asking結案，且RS_FEE_REPAY狀態改為已處理
            if (params.isRepay) {
                const it = await Utils.Redmine.User.findOneByEmail(
                    '<EMAIL>',
                );
                if (it.data) {
                    watchers.push(it.data);
                }
            }

            const askingId = await this.publish({
                assignedId: assigned.id,
                watcherIds: watchers.map((user) => user.id),
                subject: `美國運通卡補刷 ${input.orderType} 訂閱制-${input.memberName}/${input.memberCode}/${params.period}`,
                descriptionInput: {
                    productType: input.orderType,
                    period: input.period,
                    date: moment().format('YYYY-MM-DD'),
                    orderCode: input.orderCode,
                    shipCode: input.shipCode,
                    memberCode: input.memberCode,
                    memberName: input.memberName,
                    storeName: `${input.storeCode}-${input.storeName}`,
                    userName: input.userName,
                    cardNumber: input.cardNumber,
                    errorMessage: input.errorMessage,
                },
                parentAskingId: input.parentAskingId,
            });
            return askingId ?? null;
        } catch (err) {
            logger.error(`美國運通卡補刷Asking建立失敗: ${err}`);
            return null;
        }
    }

    /**
     * 處理一般刷卡失敗案件
     * @param params - 失敗參數
     * @returns Asking ID
     */
    private async failed(params: IFailedParams): Promise<number | undefined> {
        const form = await this.formService.findOne(params.applicationId);
        if (!form) {
            return;
        }

        // 取得相關人員資訊
        const personnel = await this.getPersonnelForPaymentFailure(params);

        const askingId = await this.publish({
            assignedId: personnel.assignedId,
            watcherIds: personnel.watcherIds,
            subject: `${params.orderType} 訂閱制刷卡失敗-${params.memberName}/${params.memberCode}/${params.period}`,
            descriptionInput: {
                productType: params.orderType,
                period: params.period,
                date: moment().format('YYYY-MM-DD'),
                orderCode: form.orderCode,
                shipCode: params.shipCode,
                memberCode: params.memberCode,
                memberName: params.memberName,
                storeName: `${params.storeCode}-${params.storeName}`,
                userName: params.userName,
                cardNumber: params.cardNumber,
                errorMessage: params.errorMessage,
            },
        });
        return askingId;
    }

    /**
     * 取得付款失敗案件的相關人員資訊
     * @param params - 包含門市代碼、用戶代碼、訂單類型的參數
     * @returns 指派人員和觀察者列表
     */
    private async getPersonnelForPaymentFailure(params: {
        storeCode: string;
        userCode: string;
        orderType: ShipperType;
    }) {
        // 取得區督導、PM、財務人員
        const [manager, PM, FINs] = await Promise.all([
            this.getManager(params.storeCode),
            this.getPM(params.orderType),
            this.getFINs(),
        ]);

        // 建立觀察者列表
        const watcherIds: number[] = [];
        if (manager) watcherIds.push(manager.id);
        if (PM) watcherIds.push(PM.id);
        if (FINs) watcherIds.push(...FINs.map((fin) => fin.id));

        // 決定指派人員
        let assignedId: number | undefined;
        const user = await Utils.Redmine.User.findOneByUserCode(
            params.userCode,
        );
        assignedId = user.data?.id;

        const storeUser = await db.User.findOne({
            where: { code: params.userCode },
        });

        if (!storeUser || !storeUser.isActive) {
            assignedId = manager?.id;
        }

        return { assignedId, watcherIds };
    }

    //刷卡失敗
    async foodPayFailed(params: {
        orderType: ShipperType;
        orderId: string;
        period: number;
        errorMessage: string;
        storeCode: string;
    }): Promise<number | undefined> {
        //watcher: 區督導 & PM & 財務人員
        let watcherIds: number[] = [];

        const orderDetails = await this.orderService.searchFood({
            orderId: params.orderId,
        });
        const order = orderDetails[0];

        //區督導
        const manager = await this.getManager(order.storeId);
        if (manager) {
            watcherIds = watcherIds.concat(manager.id);
        }
        //PM
        const PM = await this.getPM(params.orderType);
        if (PM) {
            watcherIds = watcherIds.concat(PM.id);
        }
        //財務人員
        const FINs = await this.getFINs();
        if (FINs) {
            watcherIds = watcherIds.concat(FINs.map((fin) => fin.id));
        }

        //assigned
        let assignedId;
        const user = await Utils.Redmine.User.findOneByUserCode(order.userId);
        assignedId = user.data?.id;
        const storeUser = await db.User.findOne({
            where: { code: order.userId },
        });
        if (!storeUser || !storeUser.isActive) {
            assignedId = manager?.id;
        }

        const askingId = await this.publish({
            assignedId,
            watcherIds,
            subject: `${params.orderType} 食品訂閱制刷卡失敗-${order.memberName}/${order.memberCode}/${order.period}`,
            descriptionInput: {
                productType: params.orderType,
                period: params.period,
                date: moment().format('YYYY-MM-DD'),
                orderId: order.orderId ?? '',
                memberCode: order.memberCode,
                memberName: order.memberName,
                storeName: `${order.storeId}-${order.storeName}`,
                userName: order.userId,
                cardNumber: this.maskCardNaumber(order.cardNumber),
                errorMessage: params.errorMessage,
            },
        });
        return askingId;
    }

    /**
     * 處理 ACH 扣款失敗案件
     * @param params - ACH 失敗參數
     * @returns Asking ID
     */
    private async ACHFailed(
        params: IACHFailedParams,
    ): Promise<number | undefined> {
        const form = await this.formService.findOne(params.applicationId);
        if (!form) {
            return;
        }

        // 取得相關人員資訊
        const personnel = await this.getPersonnelForPaymentFailure(params);

        const askingId = await this.publish({
            assignedId: personnel.assignedId,
            watcherIds: personnel.watcherIds,
            subject: `${params.orderType} 訂閱制銀行扣款失敗-${params.memberName}/${params.memberCode}/${params.period}`,
            descriptionInput: {
                productType: params.orderType,
                period: params.period,
                date: moment().format('YYYY-MM-DD'),
                orderCode: form.orderCode,
                shipCode: params.shipCode,
                memberCode: params.memberCode,
                memberName: params.memberName,
                storeName: `${params.storeCode}-${params.storeName}`,
                userName: params.userName,
                bankCode: params.bankCode,
                bankAccount: params.bankAccount,
                errorMessage: params.errorMessage,
            },
        });

        // 寫入 askingId
        if (!askingId) {
            throw new Error('askingId is undefined');
        }

        await this.repayService.updateAskingId({
            filters: {
                applicationCode: params.applicationId,
                period: params.period,
            },
            askingId,
        });

        return askingId;
    }

    /**
     * 處理補刷卡成功的情況
     * @param params - 補刷完成參數
     */
    async repayCompleted(params: IRepayCompletedParams): Promise<void> {
        try {
            // 1. 取得申請書和失敗記錄
            const { application, failed } =
                await this.getApplicationAndFailedRecord(params);

            // 2. 更新補刷狀態為成功
            await this.repayService.updateStateSuccess({
                applicationCode: params.applicationId,
                period: params.period,
            });

            // 3. 處理發票開立
            const invoiceResult = await this.handleInvoiceCreation(failed);

            // 4. 更新 Asking 案件為已完成
            await this.updateAskingAsCompleted(failed.askingId, {
                cardNumber: application?.cardNumber,
                invoiceResult,
            });
        } catch (err) {
            logger.error(`update asking completed error: ${err}`);
        }
    }

    /**
     * 取得申請書和失敗記錄
     * @param params - 包含申請ID和期數的參數
     * @returns 申請書和失敗記錄
     */
    private async getApplicationAndFailedRecord(params: {
        applicationId: string;
        period: number;
    }) {
        const [application, failed] = await Promise.all([
            this.formService.findOne(params.applicationId),
            this.repayService.findOneOrNull({
                applicationId: params.applicationId,
                period: params.period,
            }),
        ]);

        if (!failed) {
            throw new Error(
                `RS_FEE_REPAY not found(${params.applicationId},${params.period})`,
            );
        }

        return { application, failed };
    }

    /**
     * 處理發票開立
     * @param failed - 失敗記錄
     * @returns 發票處理結果
     */
    private async handleInvoiceCreation(failed: any) {
        const invoice = await this.createInvoiceBySoap(
            failed.storeId,
            failed.shipperId,
        );

        let invoiceAskingId: number | undefined;
        if (!invoice.invoiceCode || invoice.errorMessage) {
            invoiceAskingId = await this.invoiceFailed({
                shipCode: failed.shipperId,
                errorMessage: invoice.errorMessage,
            });
        }

        return {
            invoiceCode: invoice.invoiceCode,
            invoiceAskingId,
        };
    }

    /**
     * 更新 Asking 案件為已完成（信用卡）
     * @param askingId - Asking ID
     * @param details - 詳細資訊
     */
    private async updateAskingAsCompleted(
        askingId: number,
        details: {
            cardNumber?: string;
            invoiceResult?: { invoiceCode?: string; invoiceAskingId?: number };
        },
    ): Promise<void> {
        const { cardNumber, invoiceResult } = details;

        await this.update({
            askingId,
            notes: `補刷成功，系統自動結案。
                刷卡卡號: ${this.maskCardNaumber(cardNumber)}
                補開發票: ${
                    invoiceResult?.invoiceAskingId
                        ? `失敗 #${invoiceResult.invoiceAskingId}`
                        : invoiceResult?.invoiceCode || '未處理'
                }`,
            isClosed: true,
        });
    }

    async repayFoodCompleted(params: { orderId: string; cardNumber: string }) {
        try {
            const orderDetails =
                await this.orderService.getOneSubscribeDetailByOrderID(
                    params.orderId,
                );

            if (!orderDetails) {
                throw new Error(`FOOD REPAY not found(${params.orderId})`);
            }
            if (!orderDetails.askingId) {
                throw new Error(`FOOD REPAY not found(${params.orderId})`);
            }

            await this.update({
                askingId: orderDetails.askingId,
                notes: `補刷成功，系統自動結案。
                刷卡卡號: ${this.maskCardNaumber(params.cardNumber)}`,
                isClosed: true,
            });
        } catch (err) {
            logger.error(`update asking completed error: ${err}`);
        }
    }

    /**
     * 處理補 ACH 扣款成功的情況
     * @param params - 補刷完成參數
     */
    async repayACHCompleted(params: IRepayCompletedParams): Promise<void> {
        try {
            // 1. 取得失敗記錄（ACH 處理中的記錄）
            const failed = await this.repayService.findOneOrNull({
                applicationId: params.applicationId,
                period: params.period,
                isProcess: true,
            });

            if (!failed) {
                throw new Error(
                    `RS_FEE_REPAY not found(${params.applicationId},${params.period})`,
                );
            }

            // 2. 更新補刷狀態為成功
            await this.repayService.updateStateSuccess({
                applicationCode: params.applicationId,
                period: params.period,
            });

            // 3. 更新 Asking 案件為已完成（ACH）
            await this.updateAskingAsCompletedForACH(failed);
        } catch (err) {
            logger.error(`update asking completed error: ${err}`);
        }
    }

    /**
     * 更新 Asking 案件為已完成（ACH）
     * @param failed - 失敗記錄
     */
    private async updateAskingAsCompletedForACH(failed: any): Promise<void> {
        await this.update({
            askingId: failed.askingId,
            notes: `補扣款成功，系統自動結案。
                銀行行號: ${failed.bankCode}
                銀行行戶: ${this.maskBankAccount(failed.bankAccount)}`,
            isClosed: true,
        });
    }

    async invoiceFailed(
        params: IInvoiceFailedParams,
    ): Promise<number | undefined> {
        try {
            const assigned = await Utils.Redmine.User.findOneByUserCode(
                'L1317',
            );
            const user = await Utils.Redmine.User.findOneByUserCode('L1500');
            const askingId = await this.publish({
                assignedId: assigned.data?.id,
                watcherIds: user.data ? [user.data.id] : [],
                subject: `訂閱制補開發票失敗-${params.shipCode}`,
                descriptionInput: params.errorMessage,
            });
            return askingId;
        } catch (err) {
            throw err;
        }
    }

    //補刷卡-失敗
    async repayFailed(params: IRepayFailedParams) {
        try {
            const application = await this.formService.findOne(
                params.applicationId,
            );
            const failed = await this.repayService.findOneOrNull({
                applicationId: params.applicationId,
                period: params.period,
            });
            if (!failed) {
                throw new Error(
                    `RS_FEE_REPAY not found(${params.applicationId},${params.period})`,
                );
            }
            await this.update({
                askingId: failed.askingId,
                notes: `
                刷卡卡號: ${this.maskCardNaumber(application?.cardNumber)}
                失敗原因: ${params.errorMessage}`,
                isClosed: false,
            });
            logger.warn(
                `補刷-出貨單"${failed.shipperId}"刷卡結果失敗: ${params.errorMessage}(#${failed.askingId})`,
            );
        } catch (err) {
            logger.error(`update asking failed error: ${err}`);
        }
    }

    async repayFoodFailed(params: {
        orderId: string;
        cardNumber: string;
        errorMessage: string;
    }) {
        try {
            const orderDetails =
                await this.orderService.getOneSubscribeDetailByOrderID(
                    params.orderId,
                );

            if (!orderDetails) {
                throw new Error(`FOOD REPAY not found(${params.orderId})`);
            }
            if (!orderDetails.askingId) {
                throw new Error(`FOOD REPAY not found(${params.orderId})`);
            }

            await this.update({
                askingId: orderDetails.askingId,
                notes: `
                刷卡卡號: ${this.maskCardNaumber(params.cardNumber)}
                失敗原因: ${params.errorMessage}`,
                isClosed: false,
            });
            logger.warn(
                `補刷-健康食品訂閱刷卡結果失敗(#${orderDetails.askingId})`,
            );
        } catch (err) {
            logger.error(`update asking failed error: ${err}`);
        }
    }

    //補ACH-失敗
    async repayACHFailed(params: IRepayFailedParams) {
        try {
            const failed = await this.repayService.findOneOrNull({
                applicationId: params.applicationId,
                period: params.period,
                isProcess: true,
            });
            if (!failed) {
                throw new Error(
                    `RS_FEE_REPAY not found(${params.applicationId},${params.period})`,
                );
            }

            await this.repayService.updateStateFailedForACH({
                applicationCode: params.applicationId,
                period: params.period,
            });

            await this.update({
                askingId: failed.askingId,
                notes: `
                銀行行號: ${failed.bankCode}
                銀行行戶: ${this.maskBankAccount(failed.bankAccount)}
                失敗原因: ${params.errorMessage}`,
                isClosed: false,
            });
            logger.warn(
                `補扣款-出貨單"${failed.shipperId}"扣款結果失敗: ${params.errorMessage}(#${failed.askingId})`,
            );
        } catch (err) {
            logger.error(`update asking failed error: ${err}`);
        }
    }

    /**
     * 處理連續3期刷卡失敗案件
     * @param params - 失敗參數
     * @returns Asking ID
     */
    private async repayFailed3ConsecutivePeriods(
        params: IFailedParams,
    ): Promise<number | undefined> {
        return await this.createConsecutiveFailureAsking(params, {
            consecutivePeriods: 3,
            subjectSuffix: '存證信函',
            watcherConfig: {
                includeFIN: true,
                includePM: true,
                includeManager: true,
            },
        });
    }

    /**
     * 建立連續失敗期數的 Asking 案件
     * @param params - 失敗參數
     * @param config - 配置選項
     * @returns Asking ID
     */
    private async createConsecutiveFailureAsking(
        params: IFailedParams,
        config: {
            consecutivePeriods: number;
            subjectSuffix: string;
            watcherConfig: {
                includeFIN?: boolean;
                includePM?: boolean;
                includeManager?: boolean;
                includeLegal?: boolean;
            };
        },
    ): Promise<number | undefined> {
        const form = await this.formService.findOne(params.applicationId);
        if (!form) {
            return;
        }

        const member = await db.Member.findOne({
            where: { clientCode: form.memberCode },
        });

        // 取得相關人員資訊
        const personnel = await this.getPersonnelForConsecutiveFailure(
            params,
            config.watcherConfig,
        );

        const askingId = await this.publish({
            assignedId: personnel.assignedId,
            watcherIds: personnel.watcherIds,
            subject: `${params.orderType} 訂閱制刷卡連續 ${config.consecutivePeriods} 期失敗/${config.subjectSuffix}-${member?.name}/${form.memberCode}`,
            descriptionInput: {
                productType: params.orderType,
                period: params.period,
                date: moment().format('YYYY-MM-DD'),
                orderCode: form.orderCode,
                shipCode: params.shipCode,
                memberCode: params.memberCode,
                memberName: params.memberName,
                storeName: `${params.storeCode}-${params.storeName}`,
                userName: params.userName,
                cardNumber: params.cardNumber,
                errorMessage: params.errorMessage,
            },
        });

        return askingId;
    }

    /**
     * 取得連續失敗案件的相關人員資訊
     * @param params - 失敗參數
     * @param watcherConfig - 觀察者配置
     * @returns 指派人員和觀察者列表
     */
    private async getPersonnelForConsecutiveFailure(
        params: IFailedParams,
        watcherConfig: {
            includeFIN?: boolean;
            includePM?: boolean;
            includeManager?: boolean;
            includeLegal?: boolean;
        },
    ) {
        const watcherIds: number[] = [];

        // 根據配置添加不同類型的觀察者
        if (watcherConfig.includeFIN) {
            const FINs = await this.getFINs();
            if (FINs) watcherIds.push(...FINs.map((fin) => fin.id));
        }

        if (watcherConfig.includePM) {
            const PM = await this.getPM(params.orderType);
            if (PM) watcherIds.push(PM.id);
        }

        if (watcherConfig.includeManager) {
            const manager = await this.getManager(params.storeCode);
            if (manager) watcherIds.push(manager.id);
        }

        if (watcherConfig.includeLegal) {
            const legal = await Utils.Redmine.User.findOneByUserCode('L1408');
            if (legal.data) watcherIds.push(legal.data.id);
        }

        // 決定指派人員
        let assignedId: number | undefined;
        const user = await Utils.Redmine.User.findOneByUserCode(
            params.userCode,
        );
        assignedId = user.data?.id;

        const storeUser = await db.User.findOne({
            where: { code: params.userCode },
        });

        if (!storeUser || !storeUser.isActive) {
            const manager = await this.getManager(params.storeCode);
            assignedId = manager?.id;
        }

        return { assignedId, watcherIds };
    }

    /**
     * 處理連續4期刷卡失敗案件
     * @param params - 失敗參數
     * @returns Asking ID
     */
    private async repayFailed4ConsecutivePeriods(
        params: IFailedParams,
    ): Promise<number | undefined> {
        return await this.createConsecutiveFailureAsking(params, {
            consecutivePeriods: 4,
            subjectSuffix: '法律訴訟',
            watcherConfig: {
                includePM: true,
                includeManager: true,
                includeLegal: true,
            },
        });
    }

    private async getPM(
        orderType: ShipperType,
    ): Promise<Utils.Redmine.User.User | null | undefined> {
        switch (orderType) {
            case ShipperType.HA: {
                const user = await Utils.Redmine.User.findOneByUserCode(
                    'L1631',
                );
                return user.success && user.data ? user.data : null;
            }
            case ShipperType.RS:
            case ShipperType.RSM: {
                const user = await Utils.Redmine.User.findOneByUserCode(
                    'L1097',
                );
                return user.success && user.data ? user.data : null;
            }
            case ShipperType.FOOD: {
                const user = await Utils.Redmine.User.findOneByUserCode(
                    'L1341',
                );
                return user.success && user.data ? user.data : null;
            }
        }
    }

    private async getFINs(): Promise<Utils.Redmine.User.User[]> {
        const users: Utils.Redmine.User.User[] = [];
        let res = await Utils.Redmine.User.findOneByUserCode('L1095');
        if (res.success && res.data) {
            users.push(res.data);
        }

        switch (this.company) {
            // HA Lily Lee[李采凌] L1719 <EMAIL>,
            case CLINICO: {
                res = await Utils.Redmine.User.findOneByUserCode('L1719');
                if (res.success && res.data) {
                    users.push(res.data);
                }
                break;
            }
            // SKD Jenny Yang[楊卉溱] L1700 <EMAIL>
            case SKD:
                res = await Utils.Redmine.User.findOneByUserCode('L1700');
                if (res.success && res.data) {
                    users.push(res.data);
                }
                break;
            // IB Felix Shao[邵子懿] L2015 <EMAIL>
            case IB:
                res = await Utils.Redmine.User.findOneByUserCode('L2015');
                if (res.success && res.data) {
                    users.push(res.data);
                }
                break;
        }
        return users;
    }

    private async getFINsForAmericanExpress(): Promise<{
        assigned: Utils.Redmine.User.User;
        watchers: Utils.Redmine.User.User[];
    }> {
        let assigned;
        const watchers: Utils.Redmine.User.User[] = [];
        let res = await Utils.Redmine.User.findOneByUserCode('L1095');
        if (res.success && res.data) {
            watchers.push(res.data);
        }

        switch (this.company) {
            // HA Lily Lee[李采凌] L1719 <EMAIL>,
            case CLINICO: {
                res = await Utils.Redmine.User.findOneByUserCode('L1719');
                if (res.success && res.data) {
                    assigned = res.data;
                }
                break;
            }
            // SKD Jenny Yang[楊卉溱] L1700 <EMAIL>
            case SKD:
                res = await Utils.Redmine.User.findOneByUserCode('L1700');
                if (res.success && res.data) {
                    assigned = res.data;
                }
                break;
            // IB Felix Shao[邵子懿] L2015 <EMAIL>
            case IB:
                res = await Utils.Redmine.User.findOneByUserCode('L2015');
                if (res.success && res.data) {
                    assigned = res.data;
                }
                break;
        }
        return { assigned, watchers };
    }

    private async getManager(
        storeCode: string,
    ): Promise<Utils.Redmine.User.User | null> {
        const store = await db.Store.findOne({ where: { code: storeCode } });
        if (!store) {
            return null;
        }

        const users = managers.filter(
            (manager) =>
                manager.zone1 == store.zoneId && store.zone2Id == manager.zone2,
        );
        if (users.length != 1) {
            return null;
        }

        const user = await Utils.Redmine.User.findOneByUserCode(users[0].code);
        return user.success && user.data ? user.data : null;
    }

    private maskCardNaumber(cardNumber?: string): string {
        if (!cardNumber) {
            return '';
        }
        if (cardNumber.length <= 4) {
            return cardNumber;
        } else {
            return (
                stringUtil.paddingRightForString(
                    '',
                    cardNumber.length - 4,
                    '*',
                ) +
                '-' +
                cardNumber.substring(cardNumber.length - 4, cardNumber.length)
            );
        }
    }

    private maskBankAccount(bankAccount?: string): string {
        if (!bankAccount) {
            return '';
        }
        if (bankAccount.length <= 5) {
            return bankAccount;
        } else {
            return (
                stringUtil.paddingRightForString(
                    '',
                    bankAccount.length - 5,
                    '*',
                ) +
                '-' +
                bankAccount.substring(
                    bankAccount.length - 5,
                    bankAccount.length,
                )
            );
        }
    }

    private generateDescriptionTemplate(
        description: IAskingDescriptionParams,
    ): string {
        return `
        商品: ${description.productType}
        訂閱期數: ${description.period}
        刷卡日期: ${description.date}
        訂單單號: ${description.orderCode}
        出貨單號: ${description.shipCode}
        會員編號: ${description.memberCode}
        會員姓名: ${description.memberName}
        所屬門市: ${description.storeName}
        負責聽力師: ${description.userName}
        刷卡卡號: ${description.cardNumber}
        失敗原因: ${description.errorMessage}`;
    }

    private generateFoodDescriptionTemplate(
        description: IFoodAskingDescriptionParams,
    ): string {
        return `
        商品: ${description.productType}
        訂閱期數: ${description.period}
        刷卡日期: ${description.date}
        訂單單號: ${description.orderId}
        會員編號: ${description.memberCode}
        會員姓名: ${description.memberName}
        所屬門市: ${description.storeName}
        負責聽力師: ${description.userName}
        刷卡卡號: ${description.cardNumber}
        失敗原因: ${description.errorMessage}`;
    }

    private generateACHDescriptionTemplate(
        description: IAskingACHDescriptionParams,
    ): string {
        return `
        商品: ${description.productType}
        訂閱期數: ${description.period}
        扣款日期: ${description.date}
        訂單單號: ${description.orderCode}
        出貨單號: ${description.shipCode}
        會員編號: ${description.memberCode}
        會員姓名: ${description.memberName}
        所屬門市: ${description.storeName}
        負責聽力師: ${description.userName}
        銀行行號: ${description.bankCode}
        銀行行戶: ${description.bankAccount}
        失敗原因: ${description.errorMessage}
        
        重新執行銀行扣款請至訂閱申請書內的 *需補刷(手動)* 
        `;
    }

    private async createInvoiceBySoap(
        storeCode: string,
        shipCode: string,
    ): Promise<{
        invoiceCode?: string;
        errorMessage: string;
    }> {
        try {
            const data = this.generateData(storeCode, shipCode);
            const invoice = await this.soapService.createInvoice(data);
            return invoice;
        } catch (err) {
            logger.error(`[${this.company}]${shipCode}補開發票失敗: ${err} `);
            return {
                errorMessage: err.message,
            };
        }
    }

    private generateData(storeCode: string, shipCode: string): object {
        return {
            // request structure
            Request: {
                Access: {
                    Authentication: {
                        $: { user: 'tiptop', password: '' },
                    },
                    Connection: {
                        $: {
                            application: 'resmed-vpos',
                            source: ip.address(),
                        },
                    },
                    Organization: { $: { name: storeCode } },
                    Locale: { $: { language: 'zh_tw' } },
                },
                RequestContent: {
                    Document: {
                        RecordSet: [
                            {
                                $: { id: '1' },
                                Master: {
                                    $: { name: 'oga_file' },
                                    Record: {
                                        Field: [
                                            {
                                                $: {
                                                    name: 'oga01',
                                                    value: shipCode,
                                                },
                                            },
                                        ],
                                    },
                                },
                            },
                        ],
                    },
                },
            },
        };
    }
}
