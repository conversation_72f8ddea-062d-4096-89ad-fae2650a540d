import {
    ACHRespondData,
    CreateShipperParams,
    CreateShipperResult,
    UpdatePaymentResult,
    VposRespondData,
} from './interfaces/payment.interface';
import { SOAPService } from './soap.service';
import * as moment from 'moment';
import { OrderService } from './order.service';
import {
    IFoodOrderForm,
    IWPCShipmentParams,
    SearchOrderResult,
} from './interfaces/order.interface';
import logger from './logger.service';
import { IAccessParams } from './interfaces/soap.interface';
import { ISubscribeDetail } from './interfaces/subscribe.interface';

export const WpcMethodRegister = {
    CreateSoDataRequest: () => {
        return 'CreateSoDataAsync';
    },
    CreateShipOrders: () => {
        return 'CreateShipOrdersAsync';
    },
    CreateCCSubShipping: () => {
        return 'CreateCCSubShippingAsync';
    },
};

export class WpcService {
    private company: string;
    private soapService = new SOAPService();
    private orderService: OrderService;

    constructor(company: string) {
        this.company = company;
        this.orderService = new OrderService(this.company);
    }

    async createShippers(
        orders: SearchOrderResult[],
        shipDate?: Date,
    ): Promise<CreateShipperResult[]> {
        const res: CreateShipperResult[] = [];
        if (orders.length != 0) {
            for (const order of orders) {
                let shipperId = await this.orderService.checkShipperId(order);
                if (!shipperId) {
                    //產生出貨單
                    shipperId = await this.createShipper(
                        {
                            orderId: order.orderId,
                            itemCode: order.itemCode,
                            storeId: order.storeId,
                            period: order.period,
                            line: order.line,
                            items: order.items,
                        },
                        shipDate,
                    );
                }
                if (shipperId) {
                    res.push({
                        shipperId,
                        method: order.method,
                        orderId: order.orderId,
                        applicationId: order.applicationId,
                        period: order.period,
                        amount: order.amount,
                    });
                } else {
                    logger.error(
                        `[${this.company}]出貨單${order.orderId}: shipperId is null `,
                    );
                }
            }
        }
        return res;
    }

    async updateShippers(
        respondData: VposRespondData[],
    ): Promise<UpdatePaymentResult[]> {
        const res: UpdatePaymentResult[] = [];
        for (const order of respondData) {
            //將交易結果傳更新到出貨單(信用卡)
            await this.orderService.updatePaymentStatus({
                shipperId: order.shipperId,
                cardNumber: order.cardNumber,
                applicationId: order.applicationId,
                period: order.period,
                respondCode: order.respondCode,
                authorizationCode: order.authorizationCode,
            });

            let errorMessage = '';
            if (order.respondCode != '00') {
                errorMessage = `出貨單"${order.shipperId}"刷卡結果失敗: ${order.respondMessage}`;
            }

            res.push({
                applicationId: order.applicationId,
                period: order.period,
                shipperId: order.shipperId,
                respondCode: order.respondCode,
                respondMessage: order.respondMessage,
                errorCode: order.errorCode,
                errorMessage,
                orderType: order.orderType,
                storeId: order.storeId,
                amount: order.amount,
                method: '2',
            });
        }

        return res;
    }

    async updateACHShippers(
        orders: ACHRespondData[],
    ): Promise<UpdatePaymentResult[]> {
        const res: UpdatePaymentResult[] = [];
        for (const order of orders) {
            //將交易結果傳更新到出貨單(ACH)
            await this.orderService.updatePaymentStatus({
                shipperId: order.shipperId,
                cardNumber: order.bankAccount,
                applicationId: order.applicationId,
                period: order.period,
                respondCode: order.respondCode,
                authorizationCode: order.authorizationCode,
            });

            let errorMessage = '';
            if (order.respondCode != '00') {
                errorMessage = `出貨單"${order.shipperId}"扣款結果失敗: ${order.respondMessage}`;
            }

            res.push({
                applicationId: order.applicationId,
                period: order.period,
                shipperId: order.shipperId,
                respondCode: order.respondCode,
                respondMessage: order.respondMessage,
                errorCode: errorMessage ? order.respondCode : '',
                errorMessage,
                orderType: order.orderType,
                storeId: order.storeId,
                amount: order.amount,
                method: '3',
            });
        }

        return res;
    }

    async createShipper(
        params: CreateShipperParams,
        shipDate?: Date,
    ): Promise<string | null> {
        try {
            const data = this.generateData(params, shipDate);
            const res = await this.soapService.createShipOrder({
                organization: params.storeId,
                data,
            });
            //{"Parameter":{"Record":{"Field":[{"name":"oga01","value":"RS501-N02004170006"},{"name":"code","value":""},{"name":"description","value":""}]}},"Document":{}}
            console.log(JSON.stringify(res));
            return res.shipCode;
        } catch (err) {
            logger.error(`[${this.company}]出貨單${params.orderId}: ${err} `);
            return null;
        }
    }

    async createFoodOrders(
        orders: IFoodOrderForm[],
        shipDate?: Date,
    ): Promise<ISubscribeDetail[]> {
        const momentObj = moment(shipDate);

        const subscribeDetails: ISubscribeDetail[] = [];
        for (const order of orders) {
            const shipperId = await this.createFoodOrder(order, shipDate);

            if (shipperId) {
                const subscribeDetail: ISubscribeDetail = {
                    company: order.company, // 公司別
                    rsId: order.rsId, // 訂閱申請書單號
                    orderSeqId: order.orderSeqId + 1, // 訂單項次
                    paymentPeriod: order.period + 1, // 繳費期別
                    estimatedCardDate: momentObj.format('YYYY-MM-DD'), // 預計刷卡日
                    estimatedShippingDate: momentObj.format('YYYY-MM-DD'), // 預計出貨日
                    paymentType: '2', // 繳款方式2:信用卡
                    creditCardNumber: order.cardNumber, // 信用卡號
                    creditCardExpiredDate: order.validThru, // 信用卡過期日
                    expectedCardAmount: order.amount, // 預計刷卡金額
                    orderId: shipperId, // 訂單單號
                    // shippingId: string;  // 出貨單號
                    paymentRemark: `vpos第${order.period + 1}次刷卡`, // 備註
                    // isProcessed: string;  // 執行刷卡否
                    // isCompleted: string;  // 成功否刷卡異常否?
                    // isReCard: string;  // 補刷否
                    createdBy: 'tiptop', // 資料建立者 `tiptop`
                    createdGroup: 'tiptop', // 資料建立部門 `tiptop`
                    createdDate: momentObj.format('YYYY-MM-DD'), // 建立日期
                    createdTime: momentObj.format('HH:mm:ss'), // 建立時間
                    storeId: order.storeId, // 所屬營運中心
                    legal: order.legal, // 所屬法人碼
                    regOrderDay: order.regOrderDay, // 定期訂購日
                    nextShippingDate: order.nextShippingDate, // 下次出貨日
                };
                subscribeDetails.push(subscribeDetail);

                if (order.isRepay) {
                    //更新orderId到交易明細
                    await this.orderService.updateSubscribeOrderId(
                        order.rsId,
                        order.orderSeqId,
                        shipperId,
                    );
                } else {
                    //新增本期交易明細
                    await this.orderService.createSubscribeDetail(
                        subscribeDetail,
                    );
                }

                console.log('create success');
            } else {
                logger.error(
                    `[${this.company}]出貨單${order.rsId}: shipperId is null `,
                );
            }
        }
        return subscribeDetails;
    }

    async createFoodOrder(
        order: IFoodOrderForm,
        shipDate?: Date,
    ): Promise<string | null> {
        const accessParams: IAccessParams = {
            application: 'resmed-vpos',
            name: order.storeId,
        };
        const body = this.generateFoodData(order, shipDate);
        const result = await this.soapService.sendRequest(
            WpcMethodRegister.CreateSoDataRequest(),
            accessParams,
            body,
        );
        console.log(result);
        console.log(JSON.stringify(result));
        return result.shipCode;
    }

    async createFoodShipment(
        order: IWPCShipmentParams,
        shipDate?: Date,
    ): Promise<string | null> {
        const accessParams: IAccessParams = {
            application: 'resmed-vpos',
            name: order.storeId,
        };
        const body = this.generateFoodShipmentData(order, shipDate);
        try {
            const result = await this.soapService.sendRequest(
                WpcMethodRegister.CreateCCSubShipping(),
                accessParams,
                body,
            );
            console.log(JSON.stringify(result));
            return result.shipCode;
        } catch (err) {
            return null;
        }
    }

    private generateData(order: CreateShipperParams, shipDate?: Date): object {
        const date = shipDate
            ? moment(shipDate).format('YYMMDD')
            : moment().format('YYMMDD');

        const details: object[] = [];

        if (!order.itemCode && order.items.length > 0) {
            for (const item of order.items) {
                details.push({
                    Field: [
                        { $: { name: 'oeb03', value: item.line } },
                        { $: { name: 'oeb04', value: item.materialCode } },
                        { $: { name: 'ogb12', value: '1' } },
                    ],
                });
            }
        } else {
            details.push({
                Field: [
                    { $: { name: 'oeb03', value: order.line } },
                    { $: { name: 'oeb04', value: order.itemCode } },
                    { $: { name: 'ogb12', value: '1' } },
                ],
            });
        }

        return {
            RecordSet: [
                {
                    $: { id: '1' },
                    Master: {
                        $: { name: 'oea_file' },
                        Record: {
                            Field: [
                                { $: { name: 'oea01', value: order.orderId } },
                                { $: { name: 'oga02', value: date } },
                                { $: { name: 'period', value: order.period } },
                            ],
                        },
                    },
                    Detail: {
                        $: { name: 'oeb_file' },
                        Record: [...details],
                    },
                },
            ],
        };
    }

    private generateFoodData(order: IFoodOrderForm, shipDate?: Date): object {
        const date = shipDate
            ? moment(shipDate).format('YYYY-MM-DD')
            : moment().format('YYYY-MM-DD');
        return {
            RecordSet: [
                {
                    $: { id: '1' },
                    Master: {
                        $: { name: 'oea_file' },
                        Record: {
                            Field: [
                                { $: { name: 'oea02', value: date } }, // 訂單日＝刷卡日
                                { $: { name: 'oea11', value: '9' } }, // 固定 `9`
                                { $: { name: 'oea12', value: 'SUB' } }, // 訂閱來源，固定 `SUB`
                                { $: { name: 'oea10', value: order.rsId } }, // 訂閱申請書單號
                                {
                                    $: {
                                        name: 'ta_oea27',
                                        value: order.period,
                                    },
                                }, // 訂閱刷卡期數：執行第幾期刷卡
                                {
                                    $: {
                                        name: 'oea03',
                                        value: order.memberCode,
                                    },
                                }, // 訂閱書客戶編號
                                { $: { name: 'oea14', value: order.userId } }, // 聽力師或訂單負責人
                                { $: { name: 'oea15', value: order.storeId } }, // 聽力師部門
                                { $: { name: 'oeaud02', value: 'S-NORMAL' } }, // 訂單理由碼：固定`S-NORMAL`
                            ],
                        },
                    },
                },
            ],
        };
    }

    private generateFoodShipmentData(
        order: IWPCShipmentParams,
        shipDate?: Date,
    ): object {
        const date = shipDate
            ? moment(shipDate).format('YYYY-MM-DD')
            : moment().format('YYYY-MM-DD');
        console.log(date);
        return {
            RecordSet: [
                {
                    $: { id: '1' },
                    Master: {
                        $: { name: 'oga_file' },
                        Record: {
                            Field: [
                                { $: { name: 'oga02', value: date } }, // 訂單日＝刷卡日
                                { $: { name: 'oea01', value: order.orderId } }, // 訂單單號
                                { $: { name: 'oea10', value: order.rsId } }, // 訂閱申請書單號
                            ],
                        },
                    },
                },
            ],
        };
    }
}
