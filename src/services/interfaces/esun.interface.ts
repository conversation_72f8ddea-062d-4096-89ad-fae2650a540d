import { UpdatePaymentParams } from './payment.interface';
import { ShipperType } from './order.interface';
import { SubscribeDetailModel } from '../../models/subscribeDetail.model';

export interface EsunAuthorizeRespondData extends UpdatePaymentParams {
    respondMessage: string;
    orderType: ShipperType;
    storeId: string;
    amount: number;
    errorCode: string;
}

export interface IGenerateAuthorizeResponseResult {
    filesName: string[];
    data: EsunAuthorizeRespondData[];
}

export interface eSunSubscribeDetailData extends SubscribeDetailModel {
    respondCode: string;
    responseMessage: string;
    cardNumber: string;
}
