import * as dotenvFlow from 'dotenv-flow';
const dotenv = dotenvFlow.config();
process.env.NODE_ENV = dotenv.parsed!.NODE_ENV;

import * as express from 'express';
import * as cors from 'cors';
import * as path from 'path';
import apiRouters from './routers';
import { endMiddlewares } from './middlewares';
import logger from './services/logger.service';
import * as basicAuth from 'express-basic-auth';
import { Utils } from '@clinico/clinico-node-framework';
import configs from './configs';

Utils.Mailer.initialize(configs.smtp);
Utils.LDAP.initialize(configs.ldap);
Utils.Redmine.initialize(configs.redmine);

(async () => {
    const app = express();
    app.use(cors());
    app.use(express.json());
    app.use(express.urlencoded({ extended: false }));

    app.use('/api', apiRouters, ...endMiddlewares);

    const users = { admin: 'admin' };
    app.use(
        basicAuth({
            challenge: true,
            authorizeAsync: true,
            authorizer: authorizeFn,
        }),
    );
    app.use('/', express.static(path.resolve(__dirname, '../views')));

    app.listen(process.env.APP_PORT, () => {
        logger.info(`server listen on ${process.env.APP_PORT}`);
    });
})();

async function authorizeFn(email: string, password: string, cbFn) {
    const passed = await Utils.LDAP.authorize(email, password);
    return cbFn(null, passed);
}
