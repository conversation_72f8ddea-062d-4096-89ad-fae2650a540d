{"name": "resmed-vpos", "version": "4.0.13", "description": "", "main": "index.js", "scripts": {"clean": "npx rimraf ./dist", "build": "npm run clean && npx tsc && cp .env* ./dist", "start:dev": "NODE_ENV=local npx tsc-watch -p tsconfig.json --onSuccess \"node dist/run.js\"", "start:dev-view": "NODE_ENV=local npx tsc-watch -p tsconfig.json --onSuccess \"node dist/app.js\"", "lint": "npx eslint \"src/**/*.ts\" --fix --max-warnings=0"}, "lint-staged": {"*.ts": ["npx prettier --write", "npx eslint --fix"]}, "author": "<PERSON>", "license": "ISC", "dependencies": {"@clinico/clinico-node-framework": "^1.12.24", "@clinico/clinico-persistence": "^2.0.9", "cors": "^2.8.5", "dotenv": "^8.2.0", "dotenv-flow": "^3.1.0", "express": "^4.17.1", "express-basic-auth": "^1.2.1", "eslint": "^8.18.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.1.0", "eslint-plugin-unused-imports": "^4.1.4", "http-errors": "^2.0.0", "iconv-lite": "^0.6.3", "ip": "^1.1.5", "knex": "^2.1.0", "lint-staged": "^13.0.3", "lodash": "^4.17.21", "mime-types": "^2.1.26", "moment": "^2.24.0", "multer": "^1.4.2", "node-fetch": "^2.6.0", "node-schedule": "^1.3.2", "oracledb": "^5.4.0", "pg": "^8.7.3", "prettier": "^2.7.1", "readline": "^1.3.0", "shelljs": "^0.8.4", "soap": "^0.31.0", "typedi": "^0.8.0", "winston": "^3.2.1", "winston-daily-rotate-file": "^4.4.2", "xml2js": "^0.4.23", "xmlbuilder": "^15.1.1"}, "devDependencies": {"@types/dotenv-flow": "^3.0.0", "@types/express": "^4.17.13", "@types/http-errors": "^1.8.2", "@types/multer": "^1.4.7", "@types/node": "^13.7.2", "@types/node-schedule": "^1.3.0", "@types/shelljs": "^0.8.10", "@typescript-eslint/eslint-plugin": "^5.30.0", "@typescript-eslint/parser": "^5.30.0", "ts-node": "^8.5.4", "tsc-watch": "^2.4.0", "tsconfig-paths": "^3.9.0", "typescript": "^4.5.5"}}